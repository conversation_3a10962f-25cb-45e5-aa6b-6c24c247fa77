-- Register server-side commands for teleporting
RegisterCommand('goto', function(source, args, rawCommand)
    local playerId = source
    local targetId = tonumber(args[1])
    
    -- If command is run from console, allow specifying both IDs
    if source == 0 then
        if #args < 2 then
            print("Console usage: goto [playerId] [targetId]")
            return
        end
        playerId = tonumber(args[1])
        targetId = tonumber(args[2])
    end
    
    if not targetId or (source == 0 and not playerId) then
        if source > 0 then
            TriggerClientEvent('chat:addMessage', source, {
                color = {190, 0, 0},
                args = {'Error', 'Please specify a valid player ID: /goto [id]'}
            })
        else
            print("Invalid player IDs provided")
        end
        return
    end
    
    local targetPed = GetPlayerPed(targetId)
    if not DoesEntityExist(targetPed) then
        if source > 0 then
            TriggerClientEvent('chat:addMessage', source, {
                color = {190, 0, 0},
                args = {'Error', 'Target player not found'}
            })
        else
            print("Target player not found")
        end
        return
    end
    
    local targetCoords = GetEntityCoords(targetPed)
    TriggerClientEvent('teleportToCoords', playerId, {
        x = targetCoords.x,
        y = targetCoords.y,
        z = targetCoords.z
    })
end, false)

RegisterCommand('bring', function(source, args, rawCommand)
    local adminId = source
    local targetId = tonumber(args[1])
    
    -- If command is run from console, allow specifying both IDs
    if source == 0 then
        if #args < 2 then
            print("Console usage: bring [adminId] [targetId]")
            return
        end
        adminId = tonumber(args[1])
        targetId = tonumber(args[2])
    end
    
    if not targetId or (source == 0 and not adminId) then
        if source > 0 then
            TriggerClientEvent('chat:addMessage', source, {
                color = {190, 0, 0},
                args = {'Error', 'Please specify a valid player ID: /bring [id]'}
            })
        else
            print("Invalid player IDs provided")
        end
        return
    end
    
    local adminPed = GetPlayerPed(adminId)
    local adminCoords = GetEntityCoords(adminPed)
    
    TriggerClientEvent('teleportToCoords', targetId, {
        x = adminCoords.x,
        y = adminCoords.y,
        z = adminCoords.z
    })
end, false)