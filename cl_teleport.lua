-- Register the teleport event
RegisterNetEvent('teleportToCoords')
AddEventHandler('teleportToCoords', function(coords)
    local playerPed = PlayerPedId()

    -- Stop any animations or tasks
    ClearPedTasksImmediately(playerPed)

    -- Check if player is in a vehicle
    local vehicle = GetVehiclePedIsIn(playerPed, false)
    local wasInVehicle = vehicle ~= 0
    local vehicleSeat = -1

    if wasInVehicle then
        -- Get the seat the player was in
        for i = -1, GetVehicleMaxNumberOfPassengers(vehicle) - 1 do
            if GetPedInVehicleSeat(vehicle, i) == playerPed then
                vehicleSeat = i
                break
            end
        end

        -- Teleport the vehicle
        SetEntityCoords(vehicle, coords.x, coords.y, coords.z)
        SetEntityHeading(vehicle, GetEntityHeading(playerPed))

        -- Wait for positioning to settle
        Citizen.Wait(100)

        -- Set vehicle on ground properly
        local ground, z = GetGroundZFor_3dCoord(coords.x, coords.y, coords.z)
        if ground then
            SetEntityCoords(vehicle, coords.x, coords.y, z)
        end

        -- Ensure player is still seated in the vehicle
        Citizen.Wait(50)
        if not IsPedInVehicle(playerPed, vehicle, false) then
            SetPedIntoVehicle(playerPed, vehicle, vehicleSeat)
        end
    else
        -- Teleport the player
        SetEntityCoords(playerPed, coords.x, coords.y, coords.z, false, false, false, true)

        -- Wait for ground to load
        Citizen.Wait(100)

        -- Set player on ground properly
        local ground, z = GetGroundZFor_3dCoord(coords.x, coords.y, coords.z)
        if ground then
            SetEntityCoords(playerPed, coords.x, coords.y, z)
        end
    end

    -- Notify player of teleport
    TriggerEvent('chat:addMessage', {
        color = {0, 190, 0},
        args = {'System', 'You have been teleported.'}
    })
end)

-- Add client-side command handlers for better help text
TriggerEvent('chat:addSuggestion', '/goto', 'Teleport to a player', {
    { name = 'id', help = 'Player server ID' }
})

TriggerEvent('chat:addSuggestion', '/bring', 'Bring a player to you', {
    { name = 'id', help = 'Player server ID' }
})
