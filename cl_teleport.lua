-- Register the teleport event
RegisterNetEvent('teleportToCoords')
AddEventHandler('teleportToCoords', function(coords)
    local playerPed = PlayerPedId()
    
    -- Stop any animations or tasks
    ClearPedTasksImmediately(playerPed)
    
    -- If player is in a vehicle
    local vehicle = GetVehiclePedIsIn(playerPed, false)
    if vehicle ~= 0 then
        SetEntityCoords(vehicle, coords.x, coords.y, coords.z)
        SetEntityHeading(vehicle, GetEntityHeading(playerPed))
    else
        -- Teleport the player
        SetEntityCoords(playerPed, coords.x, coords.y, coords.z, false, false, false, true)
    end
    
    -- Wait for ground to load
    Citizen.Wait(100)
    
    -- Set player on ground properly
    local ground, z = GetGroundZFor_3dCoord(coords.x, coords.y, coords.z)
    if ground then
        SetEntityCoords(playerPed, coords.x, coords.y, z)
    end
    
    -- Notify player of teleport
    TriggerEvent('chat:addMessage', {
        color = {0, 190, 0},
        args = {'System', 'You have been teleported.'}
    })
end)

-- Add client-side command handlers for better help text
TriggerEvent('chat:addSuggestion', '/goto', 'Teleport to a player', {
    { name = 'id', help = 'Player server ID' }
})

TriggerEvent('chat:addSuggestion', '/bring', 'Bring a player to you', {
    { name = 'id', help = 'Player server ID' }
})
